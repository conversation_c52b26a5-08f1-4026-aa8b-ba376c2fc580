import { NextRequest, NextResponse } from 'next/server'

import { logError, logApi } from '@/lib/logger'
import { supabase } from '@/lib/supabase'

// Error handling wrapper
function withError<PERSON><PERSON><PERSON>(handler: (request: NextRequest) => Promise<NextResponse>) {
  return async (request: NextRequest) => {
    try {
      return await handler(request)
    } catch (error) {
      logError('Settings API Error', error)
      return NextResponse.json(
        {
          error: 'Internal server error',
          details: error instanceof Error ? error.message : 'Unknown error'
        },
        { status: 500 }
      )
    }
  }
}

// GET - Fetch all settings (store settings, user preferences, system settings)
export const GET = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  logApi('GET', '/api/settings', undefined, undefined)

  const { searchParams } = new URL(request.url)
  const type = searchParams.get('type') // 'store', 'user', 'system', or 'all'
  const userId = searchParams.get('userId')

  try {
    const settings: {
      store?: unknown
      user?: unknown
      system?: unknown[]
    } = {}

    // Fetch store settings (always included)
    if (!type || type === 'store' || type === 'all') {
      const { data: storeSettings, error: storeError } = await supabase
        .from('store_settings')
        .select('*')
        .order('updated_at', { ascending: false })
        .limit(1)
        .single()

      if (storeError) {
        if (storeError.code === 'PGRST116') {
          // No rows returned - this is OK
          settings.store = null
        } else if (storeError.code === '42P01') {
          // Table doesn't exist
          logError('Store settings table does not exist. Please run the database migration.')
          settings.store = null
        } else {
          logError('Store settings fetch error', storeError)
          settings.store = null
        }
      } else {
        settings.store = storeSettings || null
      }
    }

    // Fetch user preferences (if userId provided)
    if (userId && (!type || type === 'user' || type === 'all')) {
      const { data: userPreferences, error: userError } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (userError) {
        if (userError.code === 'PGRST116') {
          // No rows returned - this is OK
          settings.user = null
        } else if (userError.code === '42P01') {
          // Table doesn't exist
          logError('User preferences table does not exist. Please run the database migration.')
          settings.user = null
        } else {
          logError('User preferences fetch error', userError)
          settings.user = null
        }
      } else {
        settings.user = userPreferences || null
      }
    }

    // Fetch system settings (public ones or all if admin)
    if (!type || type === 'system' || type === 'all') {
      const { data: systemSettings, error: systemError } = await supabase
        .from('system_settings')
        .select('*')
        .eq('is_public', true) // For now, only fetch public settings
        .order('setting_key')

      if (systemError) {
        if (systemError.code === '42P01') {
          // Table doesn't exist
          logError('System settings table does not exist. Please run the database migration.')
          settings.system = []
        } else {
          logError('System settings fetch error', systemError)
          settings.system = []
        }
      } else {
        settings.system = systemSettings || []
      }
    }

    const queryTime = Date.now() - startTime
    logApi('GET', '/api/settings', 200, queryTime)

    return NextResponse.json({
      success: true,
      settings,
      queryTime: `${queryTime}ms`
    })

  } catch (error) {
    const queryTime = Date.now() - startTime
    logError('Settings fetch failed', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch settings',
        details: error instanceof Error ? error.message : 'Unknown error',
        queryTime: `${queryTime}ms`
      },
      { status: 500 }
    )
  }
})

// POST - Create or update settings
export const POST = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  logApi('POST', '/api/settings', undefined, undefined)

  try {
    const body = await request.json()
    const { type, data, userId } = body

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    let result

    switch (type) {
      case 'both':
        // Handle saving both store settings and user preferences
        const results = {}

        if (storeSettings) {
          // Map frontend camelCase to database snake_case for store settings
          const storeData = {
            store_name: storeSettings.storeName,
            store_description: storeSettings.storeDescription,
            store_address: storeSettings.storeAddress,
            store_phone: storeSettings.storePhone,
            store_email: storeSettings.storeEmail,
            currency: storeSettings.currency,
            timezone: storeSettings.timezone,
            language: storeSettings.language
          }

          const { data: existingStore } = await supabase
            .from('store_settings')
            .select('id')
            .limit(1)
            .single()

          if (existingStore) {
            const { data: updatedStore, error: updateError } = await supabase
              .from('store_settings')
              .update(storeData)
              .eq('id', existingStore.id)
              .select()
              .single()

            if (updateError) throw updateError
            results.store = updatedStore
          } else {
            const { data: newStore, error: insertError } = await supabase
              .from('store_settings')
              .insert(storeData)
              .select()
              .single()

            if (insertError) throw insertError
            results.store = newStore
          }
        }

        if (userPreferences) {
          // Map frontend camelCase to database snake_case for user preferences
          const userData = {
            user_id: userId || 'default_user',
            theme: userPreferences.theme,
            notifications: userPreferences.notifications,
            dashboard_settings: userPreferences.dashboard
          }

          const { data: userPrefs, error: userError } = await supabase
            .from('user_preferences')
            .upsert(userData)
            .select()
            .single()

          if (userError) throw userError
          results.user = userPrefs
        }

        result = results
        break
      case 'store':
        // Map frontend camelCase to database snake_case
        const storeData = {
          store_name: data.storeName || data.store_name,
          store_description: data.storeDescription || data.store_description,
          store_address: data.storeAddress || data.store_address,
          store_phone: data.storePhone || data.store_phone,
          store_email: data.storeEmail || data.store_email,
          currency: data.currency,
          timezone: data.timezone,
          language: data.language,
          business_hours: data.business_hours || data.businessHours,
          tax_settings: data.tax_settings || data.taxSettings,
          receipt_settings: data.receipt_settings || data.receiptSettings
        }

        // Update or insert store settings
        const { data: existingStore } = await supabase
          .from('store_settings')
          .select('id')
          .limit(1)
          .single()

        if (existingStore) {
          // Update existing store settings
          const { data: updatedStore, error: updateError } = await supabase
            .from('store_settings')
            .update(storeData)
            .eq('id', existingStore.id)
            .select()
            .single()

          if (updateError) throw updateError
          result = updatedStore
        } else {
          // Insert new store settings
          const { data: newStore, error: insertError } = await supabase
            .from('store_settings')
            .insert(storeData)
            .select()
            .single()

          if (insertError) throw insertError
          result = newStore
        }
        break

      case 'user':
        // Map frontend camelCase to database snake_case for user preferences
        const userData = {
          user_id: userId || 'default_user',
          theme: data.theme,
          notifications: data.notifications,
          dashboard_settings: data.dashboard || data.dashboard_settings,
          display_settings: data.display_settings || data.displaySettings,
          privacy_settings: data.privacy_settings || data.privacySettings,
          accessibility_settings: data.accessibility_settings || data.accessibilitySettings
        }

        // Upsert user preferences
        const { data: userPrefs, error: userError } = await supabase
          .from('user_preferences')
          .upsert(userData)
          .select()
          .single()

        if (userError) throw userError
        result = userPrefs
        break

      case 'system':
        // Update system setting
        const { setting_key, setting_value, ...otherData } = data
        
        if (!setting_key) {
          return NextResponse.json(
            { error: 'setting_key is required for system settings' },
            { status: 400 }
          )
        }

        const { data: systemSetting, error: systemError } = await supabase
          .from('system_settings')
          .upsert({ 
            setting_key, 
            setting_value: setting_value,
            ...otherData 
          })
          .select()
          .single()

        if (systemError) throw systemError
        result = systemSetting
        break

      default:
        return NextResponse.json(
          { error: 'Invalid settings type. Must be: store, user, or system' },
          { status: 400 }
        )
    }

    const queryTime = Date.now() - startTime
    logApi('POST', `/api/settings/${type}`, 200, queryTime)

    return NextResponse.json({
      success: true,
      data: result,
      queryTime: `${queryTime}ms`
    })

  } catch (error: unknown) {
    const queryTime = Date.now() - startTime
    logError('Settings save failed', error)

    // Check if it's a table doesn't exist error
    if (error && typeof error === 'object' && 'code' in error && error.code === '42P01') {
      return NextResponse.json(
        {
          error: 'Settings tables do not exist',
          details: 'Please run the database migration first. Check database/settings_schema.sql',
          queryTime: `${queryTime}ms`,
          migrationRequired: true
        },
        { status: 424 } // 424 Failed Dependency
      )
    }

    return NextResponse.json(
      {
        error: 'Failed to save settings',
        details: error instanceof Error ? error.message : 'Unknown error',
        queryTime: `${queryTime}ms`
      },
      { status: 500 }
    )
  }
})

// PUT - Update specific setting
export const PUT = withErrorHandler(async (request: NextRequest) => {
  const startTime = Date.now()
  logApi('PUT', '/api/settings', undefined, undefined)

  try {
    const body = await request.json()
    const { type, id, data, userId } = body

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Missing required fields: type and data' },
        { status: 400 }
      )
    }

    let result

    switch (type) {
      case 'store':
        if (!id) {
          return NextResponse.json(
            { error: 'id is required for store settings update' },
            { status: 400 }
          )
        }

        const { data: updatedStore, error: storeError } = await supabase
          .from('store_settings')
          .update(data)
          .eq('id', id)
          .select()
          .single()

        if (storeError) throw storeError
        result = updatedStore
        break

      case 'user':
        if (!userId) {
          return NextResponse.json(
            { error: 'userId is required for user preferences update' },
            { status: 400 }
          )
        }

        const { data: updatedUser, error: userError } = await supabase
          .from('user_preferences')
          .update(data)
          .eq('user_id', userId)
          .select()
          .single()

        if (userError) throw userError
        result = updatedUser
        break

      case 'system':
        const { setting_key } = data
        
        if (!setting_key) {
          return NextResponse.json(
            { error: 'setting_key is required for system settings update' },
            { status: 400 }
          )
        }

        const { data: updatedSystem, error: systemError } = await supabase
          .from('system_settings')
          .update(data)
          .eq('setting_key', setting_key)
          .select()
          .single()

        if (systemError) throw systemError
        result = updatedSystem
        break

      default:
        return NextResponse.json(
          { error: 'Invalid settings type. Must be: store, user, or system' },
          { status: 400 }
        )
    }

    const queryTime = Date.now() - startTime
    logApi('PUT', `/api/settings/${type}`, 200, queryTime)

    return NextResponse.json({
      success: true,
      data: result,
      queryTime: `${queryTime}ms`
    })

  } catch (error) {
    const queryTime = Date.now() - startTime
    logError('Settings update failed', error)
    
    return NextResponse.json(
      { 
        error: 'Failed to update settings',
        details: error instanceof Error ? error.message : 'Unknown error',
        queryTime: `${queryTime}ms`
      },
      { status: 500 }
    )
  }
})
