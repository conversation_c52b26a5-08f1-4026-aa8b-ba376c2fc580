-- =====================================================
-- SETTINGS TABLES ONLY - SAFE DEPLOYMENT
-- =====================================================
-- This script adds ONLY the settings tables to your existing database
-- Safe to run on databases that already have the main schema deployed
-- 
-- 🎯 WHAT THIS ADDS:
-- ✅ store_settings table with business configuration
-- ✅ user_preferences table with user customization
-- ✅ system_settings table with system configuration
-- ✅ All necessary indexes, triggers, and policies
-- ✅ Default data for immediate use
-- 
-- 🔒 PRODUCTION SAFE:
-- • Only creates tables if they don't exist
-- • Only adds sample data if tables are empty
-- • Safe to run multiple times
-- =====================================================

-- Enable required extensions (safe if already exist)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =====================================================
-- SETTINGS TABLES CREATION
-- =====================================================

-- STORE SETTINGS TABLE - Store configuration and business details
CREATE TABLE IF NOT EXISTS store_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    store_name VARCHAR(255) NOT NULL DEFAULT 'Revantad Store',
    store_description TEXT,
    store_address TEXT,
    store_phone VARCHAR(20),
    store_email VARCHAR(255),
    currency VARCHAR(10) NOT NULL DEFAULT 'PHP',
    timezone VARCHAR(50) NOT NULL DEFAULT 'Asia/Manila',
    language VARCHAR(10) NOT NULL DEFAULT 'en',
    business_hours JSONB DEFAULT '{"monday":{"open":"08:00","close":"20:00","closed":false},"tuesday":{"open":"08:00","close":"20:00","closed":false},"wednesday":{"open":"08:00","close":"20:00","closed":false},"thursday":{"open":"08:00","close":"20:00","closed":false},"friday":{"open":"08:00","close":"20:00","closed":false},"saturday":{"open":"08:00","close":"20:00","closed":false},"sunday":{"open":"09:00","close":"18:00","closed":false}}',
    tax_settings JSONB DEFAULT '{"enabled":false,"rate":0.12,"inclusive":false}',
    receipt_settings JSONB DEFAULT '{"header":"Revantad Store","footer":"Thank you for your business!","show_logo":true}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT store_settings_name_not_empty CHECK (LENGTH(TRIM(store_name)) > 0),
    CONSTRAINT store_settings_currency_valid CHECK (currency IN ('PHP', 'USD', 'EUR', 'JPY')),
    CONSTRAINT store_settings_language_valid CHECK (language IN ('en', 'fil', 'tl')),
    CONSTRAINT store_settings_email_format CHECK (store_email IS NULL OR store_email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT store_settings_phone_format CHECK (store_phone IS NULL OR store_phone ~ '^[0-9+\-\s()]+$')
);

-- USER PREFERENCES TABLE - User-specific settings and preferences
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL DEFAULT 'default_user',
    theme VARCHAR(20) NOT NULL DEFAULT 'light',
    notifications JSONB DEFAULT '{"email":true,"push":true,"sms":false,"lowStock":true,"newDebt":true,"payments":true,"marketing":false}',
    dashboard_settings JSONB DEFAULT '{"autoRefresh":true,"refreshInterval":300000,"defaultView":"dashboard","compactMode":false,"showWelcome":true}',
    display_settings JSONB DEFAULT '{"language":"en","dateFormat":"MM/dd/yyyy","timeFormat":"12h","currency":"PHP","numberFormat":"en-US"}',
    privacy_settings JSONB DEFAULT '{"shareAnalytics":false,"allowCookies":true,"dataRetention":365}',
    accessibility_settings JSONB DEFAULT '{"highContrast":false,"largeText":false,"reduceMotion":false,"screenReader":false}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT user_preferences_theme_valid CHECK (theme IN ('light', 'dark', 'system')),
    CONSTRAINT user_preferences_user_id_not_empty CHECK (LENGTH(TRIM(user_id)) > 0),
    UNIQUE(user_id)
);

-- SYSTEM SETTINGS TABLE - System-wide configuration settings
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type VARCHAR(50) NOT NULL DEFAULT 'general',
    description TEXT,
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT system_settings_key_not_empty CHECK (LENGTH(TRIM(setting_key)) > 0),
    CONSTRAINT system_settings_type_valid CHECK (setting_type IN ('general', 'security', 'performance', 'feature', 'integration'))
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_store_settings_created_at ON store_settings(created_at);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_user_preferences_theme ON user_preferences(theme);
CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_system_settings_type ON system_settings(setting_type);
CREATE INDEX IF NOT EXISTS idx_system_settings_public ON system_settings(is_public);

-- =====================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMPS
-- =====================================================

-- Create update function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Add triggers
DROP TRIGGER IF EXISTS update_store_settings_updated_at ON store_settings;
CREATE TRIGGER update_store_settings_updated_at
    BEFORE UPDATE ON store_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_system_settings_updated_at ON system_settings;
CREATE TRIGGER update_system_settings_updated_at
    BEFORE UPDATE ON system_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- SECURITY POLICIES
-- =====================================================
ALTER TABLE store_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Enable all operations for application" ON store_settings;
DROP POLICY IF EXISTS "Enable all operations for application" ON user_preferences;
DROP POLICY IF EXISTS "Enable all operations for application" ON system_settings;

-- Create new policies
CREATE POLICY "Enable all operations for application" ON store_settings FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON user_preferences FOR ALL USING (true);
CREATE POLICY "Enable all operations for application" ON system_settings FOR ALL USING (true);

-- =====================================================
-- DEFAULT DATA INSERTION
-- =====================================================

-- Insert default store settings (only if table is empty)
INSERT INTO store_settings (store_name, store_description, store_address, store_phone, store_email)
SELECT 'Revantad Store', 'Your friendly neighborhood sari-sari store', 'Barangay Sample, City, Philippines', '+63 ************', '<EMAIL>'
WHERE NOT EXISTS (SELECT 1 FROM store_settings LIMIT 1);

-- Insert default user preferences (only if table is empty)
INSERT INTO user_preferences (user_id, theme)
SELECT 'default_user', 'light'
WHERE NOT EXISTS (SELECT 1 FROM user_preferences LIMIT 1);

-- Insert default system settings (only if table is empty)
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public) 
SELECT * FROM (VALUES
    ('app_version', '1.0.0', 'general', 'Current application version', true),
    ('maintenance_mode', 'false', 'general', 'Enable/disable maintenance mode', false),
    ('max_debt_limit', '10000', 'general', 'Maximum debt limit per customer in PHP', false),
    ('auto_backup_enabled', 'true', 'general', 'Enable automatic database backups', false),
    ('session_timeout', '3600', 'security', 'Session timeout in seconds', false),
    ('password_min_length', '8', 'security', 'Minimum password length', false),
    ('enable_2fa', 'false', 'security', 'Enable two-factor authentication', false),
    ('api_rate_limit', '100', 'performance', 'API requests per minute limit', false),
    ('cache_duration', '300', 'performance', 'Cache duration in seconds', false),
    ('enable_analytics', 'false', 'feature', 'Enable usage analytics', true),
    ('enable_notifications', 'true', 'feature', 'Enable push notifications', true),
    ('cloudinary_enabled', 'true', 'integration', 'Enable Cloudinary image uploads', false),
    ('email_service_enabled', 'false', 'integration', 'Enable email service', false)
) AS default_settings(setting_key, setting_value, setting_type, description, is_public)
WHERE NOT EXISTS (SELECT 1 FROM system_settings LIMIT 1);

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================
DO $$
BEGIN
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '🎉 SETTINGS TABLES SUCCESSFULLY DEPLOYED!';
    RAISE NOTICE '🎉 =====================================================';
    RAISE NOTICE '';
    RAISE NOTICE '✅ SETTINGS TABLES CREATED:';
    RAISE NOTICE '   🏪 Store Settings: Business configuration ready';
    RAISE NOTICE '   👤 User Preferences: User customization ready';
    RAISE NOTICE '   ⚙️ System Settings: System configuration ready';
    RAISE NOTICE '';
    RAISE NOTICE '✅ FEATURES ENABLED:';
    RAISE NOTICE '   🔒 Row Level Security policies';
    RAISE NOTICE '   ⚡ Performance indexes';
    RAISE NOTICE '   🔄 Automatic timestamp updates';
    RAISE NOTICE '   📊 Default configuration data';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Your Settings functionality is now ready to use!';
    RAISE NOTICE '   Navigate to Settings in your application sidebar';
    RAISE NOTICE '';
END $$;

-- Verification query
SELECT 'SETTINGS DEPLOYMENT VERIFICATION' as status;
SELECT 'Store Settings' as table_name, COUNT(*) as records FROM store_settings
UNION ALL SELECT 'User Preferences', COUNT(*) FROM user_preferences
UNION ALL SELECT 'System Settings', COUNT(*) FROM system_settings
ORDER BY table_name;
