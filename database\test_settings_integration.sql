-- =====================================================
-- SETTINGS INTEGRATION TEST SCRIPT
-- =====================================================
-- Test script to verify that the settings tables work correctly
-- Run this after deploying the main schema to verify functionality

-- Test 1: Verify all settings tables exist
SELECT 'Settings Tables Verification' as test_name;
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('store_settings', 'user_preferences', 'system_settings') 
        THEN '✅ EXISTS' 
        ELSE '❌ MISSING' 
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('store_settings', 'user_preferences', 'system_settings')
ORDER BY table_name;

-- Test 2: Check default data was inserted
SELECT 'Default Data Check' as test_name;
SELECT 'Store Settings' as table_name, COUNT(*) as record_count FROM store_settings
UNION ALL
SELECT 'User Preferences' as table_name, COUNT(*) as record_count FROM user_preferences  
UNION ALL
SELECT 'System Settings' as table_name, COUNT(*) as record_count FROM system_settings;

-- Test 3: Verify store settings structure and data
SELECT 'Store Settings Data' as test_name;
SELECT 
    store_name,
    currency,
    timezone,
    language,
    created_at
FROM store_settings
LIMIT 1;

-- Test 4: Verify user preferences structure and data
SELECT 'User Preferences Data' as test_name;
SELECT 
    user_id,
    theme,
    notifications,
    dashboard_settings,
    created_at
FROM user_preferences
LIMIT 1;

-- Test 5: Verify system settings
SELECT 'System Settings Sample' as test_name;
SELECT 
    setting_key,
    setting_value,
    setting_type,
    is_public
FROM system_settings
WHERE setting_key IN ('app_version', 'maintenance_mode', 'max_debt_limit')
ORDER BY setting_key;

-- Test 6: Test INSERT operations
SELECT 'Testing INSERT Operations' as test_name;

-- Test store settings update
UPDATE store_settings 
SET store_description = 'Updated: Professional sari-sari store management system'
WHERE id = (SELECT id FROM store_settings LIMIT 1);

-- Test user preferences update
UPDATE user_preferences 
SET theme = 'dark'
WHERE user_id = 'default_user';

-- Test system settings insert
INSERT INTO system_settings (setting_key, setting_value, setting_type, description, is_public)
VALUES ('test_setting', 'test_value', 'general', 'Test setting for verification', false)
ON CONFLICT (setting_key) DO UPDATE SET 
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- Test 7: Verify updates worked
SELECT 'Verification of Updates' as test_name;
SELECT 'Store Description Updated' as check_type, 
       CASE WHEN store_description LIKE '%Updated:%' THEN '✅ SUCCESS' ELSE '❌ FAILED' END as result
FROM store_settings LIMIT 1;

SELECT 'User Theme Updated' as check_type,
       CASE WHEN theme = 'dark' THEN '✅ SUCCESS' ELSE '❌ FAILED' END as result  
FROM user_preferences WHERE user_id = 'default_user';

SELECT 'System Setting Added' as check_type,
       CASE WHEN COUNT(*) > 0 THEN '✅ SUCCESS' ELSE '❌ FAILED' END as result
FROM system_settings WHERE setting_key = 'test_setting';

-- Test 8: Test JSON operations on JSONB fields
SELECT 'JSON Operations Test' as test_name;

-- Test updating notification settings
UPDATE user_preferences 
SET notifications = jsonb_set(notifications, '{email}', 'false'::jsonb)
WHERE user_id = 'default_user';

-- Test updating business hours
UPDATE store_settings 
SET business_hours = jsonb_set(business_hours, '{monday,open}', '"09:00"'::jsonb);

-- Verify JSON updates
SELECT 'Email Notifications Disabled' as check_type,
       CASE WHEN (notifications->>'email')::boolean = false THEN '✅ SUCCESS' ELSE '❌ FAILED' END as result
FROM user_preferences WHERE user_id = 'default_user';

SELECT 'Business Hours Updated' as check_type,
       CASE WHEN business_hours->'monday'->>'open' = '09:00' THEN '✅ SUCCESS' ELSE '❌ FAILED' END as result
FROM store_settings LIMIT 1;

-- Test 9: Test constraints and validation
SELECT 'Constraint Testing' as test_name;

-- This should fail due to invalid currency
DO $$
BEGIN
    BEGIN
        INSERT INTO store_settings (store_name, currency) VALUES ('Test Store', 'INVALID');
        RAISE NOTICE '❌ Currency constraint failed to work';
    EXCEPTION WHEN check_violation THEN
        RAISE NOTICE '✅ Currency constraint working correctly';
    END;
END $$;

-- This should fail due to invalid theme
DO $$
BEGIN
    BEGIN
        INSERT INTO user_preferences (user_id, theme) VALUES ('test_user', 'invalid_theme');
        RAISE NOTICE '❌ Theme constraint failed to work';
    EXCEPTION WHEN check_violation THEN
        RAISE NOTICE '✅ Theme constraint working correctly';
    END;
END $$;

-- Test 10: Performance test with indexes
SELECT 'Index Performance Test' as test_name;
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM user_preferences WHERE user_id = 'default_user';

EXPLAIN (ANALYZE, BUFFERS)
SELECT * FROM system_settings WHERE setting_key = 'app_version';

-- Test 11: Clean up test data
SELECT 'Cleaning Up Test Data' as test_name;
DELETE FROM system_settings WHERE setting_key = 'test_setting';

-- Reset to original values
UPDATE user_preferences SET theme = 'light' WHERE user_id = 'default_user';
UPDATE store_settings SET store_description = 'Your friendly neighborhood sari-sari store';

-- Final verification
SELECT 'Final Verification' as test_name;
SELECT 
    'All settings tables ready for production use!' as status,
    NOW() as test_completed_at;

-- Summary of all tables and their record counts
SELECT 'FINAL SUMMARY' as summary_type;
SELECT 'Store Settings' as table_name, COUNT(*) as records FROM store_settings
UNION ALL SELECT 'User Preferences', COUNT(*) FROM user_preferences
UNION ALL SELECT 'System Settings', COUNT(*) FROM system_settings
UNION ALL SELECT 'Products', COUNT(*) FROM products
UNION ALL SELECT 'Customers', COUNT(*) FROM customers
UNION ALL SELECT 'Customer Debts', COUNT(*) FROM customer_debts
UNION ALL SELECT 'Customer Payments', COUNT(*) FROM customer_payments
UNION ALL SELECT 'Gallery Photos', COUNT(*) FROM gallery_photos
ORDER BY table_name;
