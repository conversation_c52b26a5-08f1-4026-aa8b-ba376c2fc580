-- =====================================================
-- TEST SETTINGS SAVE FUNCTIONALITY
-- =====================================================
-- Test script to verify that store address and email saving works
-- Run this to test the field mapping fix

-- Test 1: Check current store settings
SELECT 'Current Store Settings' as test_name;
SELECT 
    store_name,
    store_description,
    store_address,
    store_phone,
    store_email,
    currency,
    timezone,
    language,
    created_at,
    updated_at
FROM store_settings;

-- Test 2: Update store settings with address and email
UPDATE store_settings 
SET 
    store_address = 'Test Address: 123 Main Street, Barangay Sample, Quezon City, Philippines',
    store_email = '<EMAIL>',
    store_phone = '+63 ************',
    store_description = 'Updated: Professional Sari-Sari Store Management System with full contact details'
WHERE id = (SELECT id FROM store_settings LIMIT 1);

-- Test 3: Verify the update worked
SELECT 'Updated Store Settings' as test_name;
SELECT 
    store_name,
    store_description,
    store_address,
    store_phone,
    store_email,
    currency,
    timezone,
    language,
    updated_at
FROM store_settings;

-- Test 4: Check if address and email are properly saved
SELECT 'Address and Email Verification' as test_name;
SELECT 
    CASE 
        WHEN store_address IS NOT NULL AND LENGTH(store_address) > 0 
        THEN '✅ Address saved successfully' 
        ELSE '❌ Address not saved' 
    END as address_status,
    CASE 
        WHEN store_email IS NOT NULL AND LENGTH(store_email) > 0 
        THEN '✅ Email saved successfully' 
        ELSE '❌ Email not saved' 
    END as email_status,
    CASE 
        WHEN store_phone IS NOT NULL AND LENGTH(store_phone) > 0 
        THEN '✅ Phone saved successfully' 
        ELSE '❌ Phone not saved' 
    END as phone_status
FROM store_settings
LIMIT 1;

-- Test 5: Test user preferences update
UPDATE user_preferences 
SET 
    theme = 'dark',
    notifications = jsonb_set(notifications, '{email}', 'true'::jsonb),
    dashboard_settings = jsonb_set(dashboard_settings, '{autoRefresh}', 'false'::jsonb)
WHERE user_id = 'default_user';

-- Test 6: Verify user preferences update
SELECT 'User Preferences Update' as test_name;
SELECT 
    user_id,
    theme,
    notifications,
    dashboard_settings,
    updated_at
FROM user_preferences
WHERE user_id = 'default_user';

-- Test 7: Test JSON field operations
SELECT 'JSON Field Operations Test' as test_name;
SELECT 
    'Email Notifications' as setting_type,
    (notifications->>'email')::boolean as value
FROM user_preferences 
WHERE user_id = 'default_user'

UNION ALL

SELECT 
    'Auto Refresh' as setting_type,
    (dashboard_settings->>'autoRefresh')::boolean as value
FROM user_preferences 
WHERE user_id = 'default_user';

-- Test 8: Reset to clean state for application testing
UPDATE store_settings 
SET 
    store_address = '',
    store_email = '',
    store_phone = '',
    store_description = 'Your friendly neighborhood sari-sari store'
WHERE id = (SELECT id FROM store_settings LIMIT 1);

UPDATE user_preferences 
SET 
    theme = 'light',
    notifications = '{"email":true,"push":true,"sms":false,"lowStock":true,"newDebt":true,"payments":true,"marketing":false}'::jsonb,
    dashboard_settings = '{"autoRefresh":true,"refreshInterval":300000,"defaultView":"dashboard","compactMode":false,"showWelcome":true}'::jsonb
WHERE user_id = 'default_user';

-- Final verification
SELECT 'Final State - Ready for Application Testing' as test_name;
SELECT 
    'Store settings reset to defaults' as status,
    COUNT(*) as store_records
FROM store_settings

UNION ALL

SELECT 
    'User preferences reset to defaults' as status,
    COUNT(*) as user_records
FROM user_preferences;

-- Show final clean state
SELECT 'Clean State Verification' as verification_type;
SELECT 
    store_name,
    COALESCE(store_address, '[EMPTY - Ready for testing]') as store_address,
    COALESCE(store_email, '[EMPTY - Ready for testing]') as store_email,
    currency,
    timezone
FROM store_settings

UNION ALL

SELECT 
    'User: ' || user_id as store_name,
    theme as store_address,
    (notifications->>'email') as store_email,
    'N/A' as currency,
    'N/A' as timezone
FROM user_preferences
WHERE user_id = 'default_user';

SELECT 'Settings tables are ready for application testing!' as final_status;
